# PLANTILLA DE CÓDIGO CRUD - Referencia para Exámenes

## 🎯 PROPÓSITO DE ESTE DOCUMENTO
Este archivo contiene la **estructura exacta de código** del proyecto CRUD de clientes para usar como **plantilla/referencia** cuando te den un contexto diferente en el examen.

**INSTRUCCIÓN PARA LA IA**: "Usa exactamente la misma estructura, estilo de código, nombres de archivos y patrones de este proyecto, pero adaptado al nuevo contexto que me proporcionen."

## 📁 ESTRUCTURA DE ARCHIVOS OBLIGATORIA
```
proyecto/
├── index.php              (Formulario principal)
├── conexion.php           (Conexión a BD)
├── registro_[entidad].php (Procesar CREATE)
├── lista_[entidad].php    (Mostrar READ)
├── editar.php             (Formulario UPDATE)
├── editar_[entidad].php   (Procesar UPDATE)
├── eliminar.php           (Procesar DELETE)
├── css/
│   └── estilos.css        (Mismos estilos)
└── img/
    ├── borrar.png
    └── cv.png
```

**IMPORTANTE**: Mantener exactamente estos nombres de archivos, solo cambiar [entidad] por el nombre correspondiente.

## 🗄️ PLANTILLA DE CONEXIÓN A BASE DE DATOS

### 📄 ARCHIVO: conexion.php (MANTENER EXACTO)
```php
<?php
$server = "localhost";
$user = "root";
$password = "";
$bd = "nombre_base_datos";  // ← SOLO CAMBIAR ESTO

$conexion = mysqli_connect($server, $user, $password, $bd);
?>
```

### 📊 ESTRUCTURA DE TABLA (Adaptar campos según contexto)
```sql
CREATE TABLE `nombre_entidad` (
  `id_entidad` int(11) NOT NULL AUTO_INCREMENT,
  `campo1` varchar(50) NOT NULL,
  `campo2` varchar(50) NOT NULL,
  `campo3` varchar(50) NOT NULL,
  `campo4` varchar(50) NOT NULL,
  `campo5` varchar(50) NOT NULL,
  `campo6` varchar(50) NOT NULL,
  `fecha_registro` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id_entidad`)
);
```

## 🔧 PLANTILLAS DE CÓDIGO CRUD

### 1️⃣ CREATE - FORMULARIO PRINCIPAL

### 📄 ARCHIVO: index.php (MANTENER ESTRUCTURA EXACTA)
```php
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" href="css/estilos.css">
    <title></title>
</head>
<body>
<h1>FORMULARIO DE REGISTRO</h1>
<form action="registro_entidad.php" method="post" class="form-register">
    <h2 class="form_titulo">Crear Nueva Cuenta</h2>
    <div class="contenedor-inputs">
        <input type="text" name="campo1" placeholder="Campo 1" required class="input-50">
        <input type="text" name="campo2" placeholder="Campo 2" required class="input-50">
        <input type="email" name="campo3" placeholder="Email" required class="input-100">
        <input type="text" name="campo4" placeholder="Campo 4" required class="input-50">
        <input type="password" name="campo5" placeholder="Contraseña" required class="input-50">
        <input type="text" name="campo6" placeholder="Campo 6" required class="input-100">
        <input type="submit" name="registro" value="Registrar" class="btn-enviar">
        <p class="form_link">¿Ya tienes una cuenta? <a href="#">Ingresa Aqui</a></p>
    </div>
</form>
</body>
</html>
```

### 📄 ARCHIVO: registro_entidad.php (MANTENER LÓGICA EXACTA)
```php
<?php
include("conexion.php");

$campo1 = $_POST['campo1'];
$campo2 = $_POST['campo2'];
$campo3 = $_POST['campo3'];
$campo4 = $_POST['campo4'];
$campo5 = $_POST['campo5'];
$campo6 = $_POST['campo6'];

$campo5 = password_hash($campo5, PASSWORD_DEFAULT);  // Si hay contraseña

$consulta = "INSERT INTO entidad (campo1, campo2, campo3, campo4, campo5, campo6) VALUES ('$campo1', '$campo2', '$campo3', '$campo4', '$campo5', '$campo6')";

$resultado = mysqli_query($conexion, $consulta);

if (!$resultado) {
    echo "Entidad no registrada";
}else{
    echo "ENTIDAD SE REGISTRO CORRECTAMENTE";
}
?>
```

### 2️⃣ READ - LISTA CON TABLA

### 📄 ARCHIVO: lista_entidad.php (MANTENER ESTRUCTURA EXACTA)
```php
<?php
include("conexion.php");
?>

<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Bootstrap demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <script>
      function eliminar(){
        var respuesta = confirm("Desea eliminar al registro");
        return respuesta
      }
    </script>
    <div class="container">
      <br>
    <center><h1>REGISTRO DE ENTIDAD</h1></center><br>

    <table class="table table-striped table-hover">
    <thead>
      <th>N</th>
      <th>Campo 1</th>
      <th>Campo 2</th>
      <th>Campo 3</th>
      <th>Campo 4</th>
      <th>Campo 5</th>
      <th>Campo 6</th>
      <th>Fecha Registro</th>
      <th>Acciones</th>
    </thead>

      <?php
      $entidad = "SELECT * FROM entidad";
      $ejecutar = mysqli_query($conexion, $entidad);

      while ($fila = mysqli_fetch_assoc($ejecutar)) {
      ?>
      <tr>
        <td><?php echo $fila["id_entidad"]; ?></td>
        <td><?php echo $fila["campo1"]; ?></td>
        <td><?php echo $fila["campo2"]; ?></td>
        <td><?php echo $fila["campo3"]; ?></td>
        <td><?php echo $fila["campo4"]; ?></td>
        <td><?php echo $fila["campo5"]; ?></td>
        <td><?php echo $fila["campo6"]; ?></td>
        <td><?php echo $fila["fecha_registro"]; ?></td>
        <td>
          <a href="editar.php?id=<?= $fila['id_entidad'] ?>"><button type="button" class="btn btn-warning"><img src="img/cv.png"></button></a>
          <a onclick="return eliminar()" href="eliminar.php?id=<?= $fila['id_entidad'] ?>"><button type="button" class="btn btn-danger"><img src="img/borrar.png"></button></a>
        </td>
      </tr>
      <?php
      }
      ?>
</table>
</div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
```

### 3️⃣ UPDATE - EDITAR REGISTRO

### 📄 ARCHIVO: editar.php (MANTENER ESTRUCTURA EXACTA)
```php
<?php
include("conexion.php");
$id = $_GET['id'];

$consulta = "SELECT * FROM entidad WHERE id_entidad='$id'";
$ejecutar = mysqli_query($conexion, $consulta);
$fila = mysqli_fetch_assoc($ejecutar);
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" href="css/estilos.css">
    <title></title>
</head>
<body>
<h1>FORMULARIO DE REGISTRO</h1>
<form action="editar_entidad.php" method="post" class="form-register">
    <h2 class="form_titulo">Crear Nueva Cuenta</h2>
    <div class="contenedor-inputs">
        <input type="hidden" name="id" value="<?= $_GET['id'] ?> ">
        <input type="text" name="campo1" placeholder="Campo 1" required class="input-50" value="<?= $fila['campo1']; ?>"  >
        <input type="text" name="campo2" placeholder="Campo 2" required class="input-50" value="<?= $fila['campo2'] ?>">
        <input type="email" name="campo3" placeholder="Email" required class="input-100" value="<?= $fila['campo3'] ?>">
        <input type="text" name="campo4" placeholder="Campo 4" required class="input-50" value="<?= $fila['campo4'] ?>">
        <input type="password" name="campo5" placeholder="Contraseña" required class="input-50" value="<?= $fila['campo5'] ?>">
        <input type="text" name="campo6" placeholder="Campo 6" required class="input-100" value="<?= $fila['campo6'] ?> ">
        <input type="submit" name="registro" value="Editar Entidad" class="btn-enviar">
    </div>
</form>
</body>
</html>
```

### 📄 ARCHIVO: editar_entidad.php (MANTENER LÓGICA EXACTA)
```php
<?php
include("conexion.php");

$id = $_POST['id'];
$campo1 = $_POST['campo1'];
$campo2 = $_POST['campo2'];
$campo3 = $_POST['campo3'];
$campo4 = $_POST['campo4'];
$campo5 = $_POST['campo5'];
$campo6 = $_POST['campo6'];

$consulta = "UPDATE entidad SET campo1='$campo1', campo2='$campo2', campo3='$campo3', campo4='$campo4', campo5='$campo5', campo6='$campo6' WHERE id_entidad='$id'";

$ejecutar = mysqli_query($conexion, $consulta);

if ($ejecutar) {
    header("Location: lista_entidad.php");
}
?>
```

### 4️⃣ DELETE - ELIMINAR REGISTRO

### 📄 ARCHIVO: eliminar.php (MANTENER EXACTO)
```php
<?php
include("conexion.php");

$id = $_GET['id'];
$consulta = "DELETE FROM entidad WHERE id_entidad='$id'";
$ejecutar = mysqli_query($conexion, $consulta);

if ($consulta) {
    header("Location: lista_entidad.php");
}
?>
```

## 🎨 ARCHIVO CSS (MANTENER EXACTO)

### 📄 ARCHIVO: css/estilos.css
```css
*{
    box-sizing: border-box;
}

body{
    margin: 0;
    font-family: sans-serif;
    background: #204862;
}

h1{
    color: #fff;
    text-align: center;
}

.form-register{
    width: 95%;
    max-width: 500%;
    margin: auto;
    background: white;
    border-radius: 7px;
}

.form_titulo{
    background: deepskyblue;
    color: #fff;
    padding: 20px;
    text-align: center;
    font-weight: 100;
    font-size: 30px;
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
    border-bottom: 5px solid crimson;
}

.contenedor-inputs{
    padding: 10px 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

input{
    margin-bottom: 15px;
    padding: 15px;
    font-size: 16px;
    border-radius: 3px;
    border: 1px solid darkgray;
}

.input-50{
    width: 48%;
}

.input-100{
    width: 100%;
}

.btn-enviar{
    background: crimson;
    color: #fff;
    margin: auto;
    padding: 10px 40px;
}

.form_link{
    width: 100%;
    margin: 7px;
    text-align: center;
    font-size: 14px;
}
```

## 📋 INSTRUCCIONES PARA LA IA EN EL EXAMEN

### 🎯 PROMPT PARA COPIAR Y PEGAR:
```
"Necesito que crees un sistema CRUD completo usando EXACTAMENTE la misma estructura, estilo de código, nombres de archivos y patrones del proyecto de referencia que te voy a proporcionar.

REQUISITOS OBLIGATORIOS:
1. Mantener la misma estructura de archivos (index.php, conexion.php, registro_entidad.php, lista_entidad.php, editar.php, editar_entidad.php, eliminar.php)
2. Usar exactamente el mismo CSS (estilos.css)
3. Mantener la misma lógica de programación y flujo de datos
4. Usar Bootstrap 5.3.7 para las tablas
5. Incluir confirmación JavaScript para eliminar
6. Usar password_hash() si hay campos de contraseña
7. Mantener la misma estructura HTML y clases CSS

SOLO CAMBIAR:
- Nombres de entidades (tabla, campos)
- Placeholders y textos según el nuevo contexto
- Nombres de variables según los nuevos campos

El nuevo contexto es: [AQUÍ PONES EL CONTEXTO DEL EXAMEN]

Usa como referencia exacta este proyecto:"
```

### ⚠️ MANTENER SIEMPRE:
1. **Estructura de archivos**: 7 archivos PHP + CSS + imágenes
2. **Lógica de programación**: Include, POST/GET, mysqli_query
3. **Estilos CSS**: Colores, clases, diseño
4. **Bootstrap**: Versión 5.3.7, clases de tabla
5. **JavaScript**: Función eliminar() con confirm()
6. **Flujo CRUD**: Create→Read→Update→Delete

