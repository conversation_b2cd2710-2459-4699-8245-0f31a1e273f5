import * as THREE from 'three';
import { PointerLockControls } from 'three/addons/controls/PointerLockControls.js';

/* ========= CONFIG ========= */
const WORLD_X = 32;
const WORLD_Z = 32;
const WORLD_Y = 16;          // altura máxima
const BLOCK_SIZE = 1;
const RENDER_DIST = 16;      // distancia a la que se generan chunks

/* ========= THREE ========= */
const scene = new THREE.Scene();
scene.background = new THREE.Color(0x87CEEB);
scene.fog = new THREE.Fog(0x87CEEB, 10, 60);

const camera = new THREE.PerspectiveCamera(75, innerWidth/innerHeight, 0.1, 100);
camera.position.set(16, 12, 16);

const renderer = new THREE.WebGLRenderer({antialias:true});
renderer.setSize(innerWidth, innerHeight);
document.body.appendChild(renderer.domElement);

/* ========= CONTROLES ========= */
const controls = new PointerLockControls(camera, document.body);
document.addEventListener('click', ()=> controls.lock());

const velocity = new THREE.Vector3();
const direction = new THREE.Vector3();
let onGround = false;

/* ========= BLOQUES ========= */
const blockTypes = {
  air:   null,
  grass: new THREE.MeshLambertMaterial({color:0x4CAF50}),
  dirt:  new THREE.MeshLambertMaterial({color:0x8B4513}),
  stone: new THREE.MeshLambertMaterial({color:0x7F7F7F}),
  water: new THREE.MeshLambertMaterial({color:0x1E90FF, transparent:true, opacity:0.7})
};
let selectedMaterial = 'grass';

/* ========= MUNDO VOXEL ========= */
const world = new Array(WORLD_Y).fill().map(()=> new Array(WORLD_X).fill().map(()=> new Array(WORLD_Z).fill('air')));

const geometry = new THREE.BoxGeometry(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
const meshes = new THREE.Group(); scene.add(meshes);

function generateWorld() {
  for (let x = 0; x < WORLD_X; x++) {
    for (let z = 0; z < WORLD_Z; z++) {
      const h = Math.floor(8 + Math.sin(x*0.1)*3 + Math.cos(z*0.1)*3);
      for (let y = 0; y < WORLD_Y; y++) {
        if (y === h) world[y][x][z] = 'grass';
        else if (y < h && y > h-3) world[y][x][z] = 'dirt';
        else if (y <= h-3) world[y][x][z] = 'stone';
        else if (y === h+1 && Math.random()<0.05) world[y][x][z] = 'water';
      }
    }
  }
}

function buildMesh(x,y,z,type) {
  if (type === 'air') return;
  const mesh = new THREE.Mesh(geometry, blockTypes[type]);
  mesh.position.set(x+0.5, y+0.5, z+0.5);
  mesh.userData = {x,y,z,type};
  meshes.add(mesh);
}

function rebuildMeshes() {
  meshes.clear();
  for (let y=0; y<WORLD_Y; y++)
    for (let x=0; x<WORLD_X; x++)
      for (let z=0; z<WORLD_Z; z++)
        buildMesh(x,y,z,world[y][x][z]);
}

generateWorld();
rebuildMeshes();
console.log('Mundo generado. Bloques en escena:', meshes.children.length);

/* ========= ILUMINACIÓN ========= */
scene.add(new THREE.AmbientLight(0xffffff, 0.6));
const sun = new THREE.DirectionalLight(0xffffff, 0.8);
sun.position.set(50,100,50);
scene.add(sun);

/* ========= RAYCASTER para colocar/destruir ========= */
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2(0,0);

function setBlock(x,y,z,type) {
  if (x<0 || x>=WORLD_X || y<0 || y>=WORLD_Y || z<0 || z>=WORLD_Z) return;
  world[y][x][z] = type;
  rebuildMeshes();
}

window.addEventListener('mousedown', (e)=>{
  if (!controls.isLocked) return;
  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(meshes.children);
  if (!intersects.length) return;

  const hit = intersects[0];
  const {x,y,z} = hit.object.userData;

  if (e.button === 0) { // izquierdo destruir
    setBlock(x,y,z,'air');
  } else if (e.button === 2) { // derecho colocar
    const pos = hit.point.add(hit.face.normal.multiplyScalar(0.5)).floor();
    setBlock(pos.x, pos.y, pos.z, selectedMaterial);
  }
});

/* ========= TECLADO ========= */
const keys = {};
onkeydown = onkeyup = e => keys[e.code]=e.type==='keydown';

function processMovement(delta) {
  if (!controls.isLocked) return;
  velocity.y -= 9.8 * delta; // gravedad
  direction.z = Number(keys['KeyW']) - Number(keys['KeyS']);
  direction.x = Number(keys['KeyD']) - Number(keys['KeyA']);
  direction.normalize().multiplyScalar(5.0 * delta);
  controls.moveRight(direction.x);
  controls.moveForward(direction.z);

  if (keys['Space'] && onGround) velocity.y = 8;
  if (keys['ShiftLeft']) velocity.y -= 15 * delta;

  camera.position.y += velocity.y * delta;
  if (camera.position.y < 2) { camera.position.y = 2; velocity.y = 0; onGround=true; } else onGround=false;
}

/* ========= SELECCIÓN DE MATERIAL ========= */
document.addEventListener('keydown', e=>{
  const map = {Digit1:'grass', Digit2:'dirt', Digit3:'stone', Digit4:'water'};
  if (map[e.code]) {
    selectedMaterial = map[e.code];
    document.getElementById('info').textContent = 'Bloque: '+selectedMaterial;
  }
});

/* ========= LOOP ========= */
let prev = performance.now();
function animate() {
  requestAnimationFrame(animate);
  const now = performance.now();
  const delta = (now - prev)/1000;
  prev = now;
  processMovement(delta);
  renderer.render(scene, camera);
}
animate();

/* ========= RESIZE ========= */
addEventListener('resize', ()=> {
  camera.aspect = innerWidth/innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(innerWidth, innerHeight);
});